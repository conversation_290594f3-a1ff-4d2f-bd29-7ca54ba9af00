#include <Wire.h>

// SC7I22 I2C地址配置
#define SC7I22_I2C_ADDR 0x19  // SDO接VDD

// SC7I22寄存器定义
#define SC7I22_WHO_AM_I     0x01
#define SC7I22_STATUS       0x0B
#define SC7I22_ACC_X_LSB    0x0C
#define SC7I22_ACC_CONF     0x40
#define SC7I22_ACC_RANGE    0x41
#define SC7I22_GYR_CONF     0x42
#define SC7I22_GYR_RANGE    0x43
#define SC7I22_I2C_CFG      0x05
#define SC7I22_BANK_SEL     0x7F
#define SC7I22_PWR_CONF     0x7D

// ESP32C3 I2C引脚定义
#define SDA_PIN 8
#define SCL_PIN 9

// 数据结构
struct IMU_Data {
  int16_t acc_x, acc_y, acc_z;
  int16_t gyr_x, gyr_y, gyr_z;
};

void setup() {
  Serial.begin(115200);
  while (!Serial) {
    delay(10);
  }
  
  Serial.println("SC7I22 Simple Test for ESP32C3");
  
  // 初始化I2C
  Wire.begin(SDA_PIN, SCL_PIN);
  Wire.setClock(100000); // 100kHz
  
  delay(500);
  
  // 检查传感器
  if (checkSC7I22Simple()) {
    Serial.println("SC7I22 detected!");
  } else {
    Serial.println("SC7I22 not found!");
    while (1) delay(1000);
  }
  
  // 简化配置 - 不进行软复位
  if (configureSC7I22Simple()) {
    Serial.println("SC7I22 configured!");
  } else {
    Serial.println("Configuration failed!");
    while (1) delay(1000);
  }
  
  Serial.println("Starting data acquisition...");
}

void loop() {
  IMU_Data imu_data;
  
  if (readSC7I22Simple(&imu_data)) {
    Serial.print(imu_data.acc_x);
    Serial.print(", ");
    Serial.print(imu_data.acc_y);
    Serial.print(", ");
    Serial.print(imu_data.acc_z);
    Serial.print(", ");
    Serial.print(imu_data.gyr_x);
    Serial.print(", ");
    Serial.print(imu_data.gyr_y);
    Serial.print(", ");
    Serial.println(imu_data.gyr_z);
  }
  
  delay(10);
}

// 简化的I2C写函数
bool writeRegSimple(uint8_t reg, uint8_t value) {
  Wire.beginTransmission(SC7I22_I2C_ADDR);
  Wire.write(reg);
  Wire.write(value);
  return (Wire.endTransmission() == 0);
}

// 简化的I2C读函数
bool readRegSimple(uint8_t reg, uint8_t* data, uint8_t len) {
  Wire.beginTransmission(SC7I22_I2C_ADDR);
  Wire.write(reg);
  if (Wire.endTransmission() != 0) return false;
  
  Wire.requestFrom(SC7I22_I2C_ADDR, len);
  for (uint8_t i = 0; i < len; i++) {
    if (Wire.available()) {
      data[i] = Wire.read();
    } else {
      return false;
    }
  }
  return true;
}

// 简化的传感器检查
bool checkSC7I22Simple() {
  uint8_t who_am_i = 0;
  
  // 切换到bank 0
  writeRegSimple(SC7I22_BANK_SEL, 0x00);
  delay(10);
  
  // 读取WHO_AM_I
  if (readRegSimple(SC7I22_WHO_AM_I, &who_am_i, 1)) {
    Serial.print("WHO_AM_I: 0x");
    Serial.println(who_am_i, HEX);
    return (who_am_i == 0x6A);
  }
  return false;
}

// 简化配置 - 跳过软复位
bool configureSC7I22Simple() {
  Serial.println("Simple configuration without soft reset...");
  
  // 确保在bank 0
  if (!writeRegSimple(SC7I22_BANK_SEL, 0x00)) {
    Serial.println("Failed to switch to bank 0");
    return false;
  }
  delay(10);
  
  // 直接配置寄存器
  if (!writeRegSimple(SC7I22_ACC_CONF, 0x88)) {
    Serial.println("Failed ACC_CONF");
    return false;
  }
  
  if (!writeRegSimple(SC7I22_ACC_RANGE, 0x01)) {
    Serial.println("Failed ACC_RANGE");
    return false;
  }
  
  if (!writeRegSimple(SC7I22_GYR_CONF, 0xC8)) {
    Serial.println("Failed GYR_CONF");
    return false;
  }
  
  if (!writeRegSimple(SC7I22_GYR_RANGE, 0x00)) {
    Serial.println("Failed GYR_RANGE");
    return false;
  }
  
  if (!writeRegSimple(SC7I22_I2C_CFG, 0x50)) {
    Serial.println("Failed I2C_CFG");
    return false;
  }
  
  delay(100);
  
  // 等待数据就绪
  uint8_t status = 0;
  uint16_t timeout = 0;
  while ((status & 0x03) != 0x03 && timeout < 5000) {
    readRegSimple(SC7I22_STATUS, &status, 1);
    timeout++;
    delay(1);
  }
  
  Serial.print("Final status: 0x");
  Serial.println(status, HEX);
  return (timeout < 5000);
}

// 简化数据读取
bool readSC7I22Simple(IMU_Data* data) {
  uint8_t raw_data[12];
  
  // 读取12字节数据
  if (!readRegSimple(SC7I22_ACC_X_LSB, raw_data, 12)) {
    return false;
  }
  
  // 解析数据（高字节在前）
  data->acc_x = (int16_t)((raw_data[1] << 8) | raw_data[0]);
  data->acc_y = (int16_t)((raw_data[3] << 8) | raw_data[2]);
  data->acc_z = (int16_t)((raw_data[5] << 8) | raw_data[4]);
  data->gyr_x = (int16_t)((raw_data[7] << 8) | raw_data[6]);
  data->gyr_y = (int16_t)((raw_data[9] << 8) | raw_data[8]);
  data->gyr_z = (int16_t)((raw_data[11] << 8) | raw_data[10]);
  
  return true;
}

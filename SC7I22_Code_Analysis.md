# SC7I22 IMU传感器代码分析

## 概述
SC7I22是一款6轴IMU传感器，包含3轴加速度计和3轴陀螺仪，支持I2C和SPI通信接口。

## 硬件配置

### I2C地址配置
- **SDO接GND**: 0x18 (7位地址)
- **SDO接VDD**: 0x19 (7位地址)
- 支持7位和8位地址模式

### 关键寄存器映射
| 寄存器地址 | 名称 | 功能描述 |
|-----------|------|----------|
| 0x01 | WHO_AM_I | 设备ID寄存器，应返回0x6A |
| 0x05 | I2C_CFG | I2C接口配置 |
| 0x0B | STATUS | 状态寄存器，bit0:ACC就绪，bit1:GYR就绪 |
| 0x0C-0x17 | 数据寄存器 | 加速度计和陀螺仪原始数据 |
| 0x40 | ACC_CONF | 加速度计配置（采样率等） |
| 0x41 | ACC_RANGE | 加速度计量程配置 |
| 0x42 | GYR_CONF | 陀螺仪配置（采样率等） |
| 0x43 | GYR_RANGE | 陀螺仪量程配置 |
| 0x7D | PWR_CONF | 电源配置寄存器 |
| 0x7F | BANK_SEL | Bank选择寄存器 |

### 数据寄存器布局（0x0C-0x17）
```
0x0C: ACC_X_LSB    0x0D: ACC_X_MSB
0x0E: ACC_Y_LSB    0x0F: ACC_Y_MSB  
0x10: ACC_Z_LSB    0x11: ACC_Z_MSB
0x12: GYR_X_LSB    0x13: GYR_X_MSB
0x14: GYR_Y_LSB    0x15: GYR_Y_MSB
0x16: GYR_Z_LSB    0x17: GYR_Z_MSB
```

## 初始化序列

### 1. 传感器检测 (SL_SC7I22_Check)
```c
1. 写入 0x7F = 0x00  // 切换到bank 0
2. 读取 0x01         // 读取WHO_AM_I
3. 验证返回值 == 0x6A
```

### 2. 传感器配置 (SL_SC7I22_Config)
```c
1. 写入 0x7D = 0x0E  // 软复位，进入正常工作模式
2. 延时 10ms
3. 写入 0x40 = 0x88  // ACC_CONF: 100Hz采样率
4. 写入 0x41 = 0x01  // ACC_RANGE: ±2G量程
5. 写入 0x42 = 0xC8  // GYR_CONF: 100Hz采样率  
6. 写入 0x43 = 0x00  // GYR_RANGE: ±2000dps量程
7. 写入 0x05 = 0x50  // I2C_CFG: I2C接口配置
8. 等待状态寄存器0x0B的bit0和bit1都为1（数据就绪）
```

## 数据读取

### 实时模式 (SL_SC7I22_RawData_Read)
```c
1. 轮询状态寄存器0x0B，等待bit0和bit1都为1
2. 读取0x0C开始的12字节数据
3. 解析数据：高字节在前，低字节在后
   - ACC_X = (raw_data[0] << 8) | raw_data[1]
   - ACC_Y = (raw_data[2] << 8) | raw_data[3]
   - ACC_Z = (raw_data[4] << 8) | raw_data[5]
   - GYR_X = (raw_data[6] << 8) | raw_data[7]
   - GYR_Y = (raw_data[8] << 8) | raw_data[9]
   - GYR_Z = (raw_data[10] << 8) | raw_data[11]
```

### FIFO模式 (SC7I22_FIFO_Read)
```c
1. 读取FIFO长度寄存器0x1F和0x20
2. 计算FIFO数据长度
3. 读取FIFO数据从0x21开始
4. 解析数据（包含时间戳）
```

## 配置参数说明

### 加速度计配置 (0x40 = 0x88)
- 采样率：100Hz
- 其他配置位根据datasheet

### 加速度计量程 (0x41 = 0x01)  
- ±2G量程

### 陀螺仪配置 (0x42 = 0xC8)
- 采样率：100Hz
- 其他配置位根据datasheet

### 陀螺仪量程 (0x43 = 0x00)
- ±2000dps量程

### I2C配置 (0x05 = 0x50)
- I2C接口相关配置

## 重要注意事项

1. **Bank切换**：某些寄存器访问需要先切换到正确的bank
2. **初始化顺序**：必须严格按照软复位→配置→等待就绪的顺序
3. **数据格式**：16位有符号整数，高字节在前
4. **状态检查**：读取数据前必须检查状态寄存器确保数据就绪
5. **延时要求**：配置寄存器后需要适当延时

## 物理单位转换

### 加速度计 (±2G量程)
```c
float acc_g = (float)raw_value * 2.0 / 32768.0;
```

### 陀螺仪 (±2000dps量程)  
```c
float gyr_dps = (float)raw_value * 2000.0 / 32768.0;
```

## 常见问题

1. **WHO_AM_I读取失败**：检查I2C连接和地址配置
2. **配置寄存器写入失败**：可能需要先进行软复位
3. **数据读取超时**：检查传感器是否正确配置和供电
4. **数据异常**：确认数据解析的字节序（高字节在前）

#include <Wire.h>

// SC7I22 I2C地址配置
// 根据SDO引脚连接确定地址：SDO接GND=0x18, SDO接VDD=0x19
#define SC7I22_I2C_ADDR 0x19  // 修改为0x18 (SDO接GND)

// SC7I22寄存器定义
#define SC7I22_WHO_AM_I     0x01
#define SC7I22_STATUS       0x0B
#define SC7I22_ACC_X_LSB    0x0C
#define SC7I22_ACC_X_MSB    0x0D
#define SC7I22_ACC_Y_LSB    0x0E
#define SC7I22_ACC_Y_MSB    0x0F
#define SC7I22_ACC_Z_LSB    0x10
#define SC7I22_ACC_Z_MSB    0x11
#define SC7I22_GYR_X_LSB    0x12
#define SC7I22_GYR_X_MSB    0x13
#define SC7I22_GYR_Y_LSB    0x14
#define SC7I22_GYR_Y_MSB    0x15
#define SC7I22_GYR_Z_LSB    0x16
#define SC7I22_GYR_Z_MSB    0x17
#define SC7I22_ACC_CONF     0x40
#define SC7I22_ACC_RANGE    0x41
#define SC7I22_GYR_CONF     0x42
#define SC7I22_GYR_RANGE    0x43
#define SC7I22_I2C_CFG      0x05
#define SC7I22_BANK_SEL     0x7F
#define SC7I22_PWR_CONF     0x7D

// ESP32C3 I2C引脚定义 (根据您的实际连接修改)
#define SDA_PIN 8
#define SCL_PIN 9

// 数据结构
struct IMU_Data {
  int16_t acc_x, acc_y, acc_z;
  int16_t gyr_x, gyr_y, gyr_z;
};

void setup() {
  Serial.begin(115200);
  while (!Serial) {
    delay(10);
  }

  Serial.println("SC7I22 IMU Test for ESP32C3");
  Serial.println("Initializing I2C...");

  // 初始化I2C
  Wire.begin(SDA_PIN, SCL_PIN);
  Wire.setClock(100000); // 降低到100kHz进行调试

  delay(500); // 增加延时

  // 先进行I2C扫描
  Serial.println("Scanning I2C bus...");
  scanI2C();

  // 检查传感器连接
  Serial.println("Checking SC7I22...");
  if (checkSC7I22()) {
    Serial.println("SC7I22 detected successfully!");
  } else {
    Serial.println("SC7I22 not found! Check connections.");
    Serial.println("Trying alternative addresses...");

    // 尝试其他可能的地址
    for (uint8_t addr = 0x18; addr <= 0x19; addr++) {
      Serial.print("Trying address 0x");
      Serial.print(addr, HEX);
      Serial.print(": ");
      if (testAddress(addr)) {
        Serial.println("Device found!");
      } else {
        Serial.println("No response");
      }
    }

    while (1) {
      delay(1000);
    }
  }

  // 配置传感器
  if (configureSC7I22()) {
    Serial.println("SC7I22 configured successfully!");
  } else {
    Serial.println("SC7I22 configuration failed!");
    while (1) {
      delay(1000);
    }
  }

  Serial.println("Starting data acquisition...");
  Serial.println("Format: AccX, AccY, AccZ, GyrX, GyrY, GyrZ");
}

void loop() {
  IMU_Data imu_data;
  
  if (readSC7I22RawData(&imu_data)) {
    // 打印原始数据
    Serial.print(imu_data.acc_x);
    Serial.print(", ");
    Serial.print(imu_data.acc_y);
    Serial.print(", ");
    Serial.print(imu_data.acc_z);
    Serial.print(", ");
    Serial.print(imu_data.gyr_x);
    Serial.print(", ");
    Serial.print(imu_data.gyr_y);
    Serial.print(", ");
    Serial.println(imu_data.gyr_z);
  } else {
    Serial.println("Failed to read data");
  }
  
  delay(10); // 100Hz采样率
}

// I2C写寄存器函数
bool writeRegister(uint8_t reg, uint8_t value) {
  Wire.beginTransmission(SC7I22_I2C_ADDR);
  Wire.write(reg);
  Wire.write(value);
  uint8_t error = Wire.endTransmission();

  if (error != 0) {
    Serial.print("Write error to reg 0x");
    Serial.print(reg, HEX);
    Serial.print(", error code: ");
    Serial.println(error);
  }

  return (error == 0);
}

// I2C读寄存器函数
bool readRegister(uint8_t reg, uint8_t* data, uint8_t len) {
  // 发送寄存器地址
  Wire.beginTransmission(SC7I22_I2C_ADDR);
  Wire.write(reg);
  uint8_t error = Wire.endTransmission(false); // 保持连接

  if (error != 0) {
    Serial.print("Read setup error for reg 0x");
    Serial.print(reg, HEX);
    Serial.print(", error code: ");
    Serial.println(error);
    return false;
  }

  // 请求数据
  uint8_t received = Wire.requestFrom(SC7I22_I2C_ADDR, len);
  if (received != len) {
    Serial.print("Expected ");
    Serial.print(len);
    Serial.print(" bytes, got ");
    Serial.println(received);
    return false;
  }

  // 读取数据
  for (uint8_t i = 0; i < len; i++) {
    if (Wire.available()) {
      data[i] = Wire.read();
    } else {
      Serial.println("No more data available");
      return false;
    }
  }

  return true;
}

// I2C总线扫描函数
void scanI2C() {
  byte error, address;
  int nDevices = 0;

  Serial.println("Scanning I2C addresses from 0x01 to 0x7F...");

  for(address = 1; address < 127; address++) {
    Wire.beginTransmission(address);
    error = Wire.endTransmission();

    if (error == 0) {
      Serial.print("I2C device found at address 0x");
      if (address < 16) Serial.print("0");
      Serial.print(address, HEX);
      Serial.println(" !");
      nDevices++;
    }
    else if (error == 4) {
      Serial.print("Unknown error at address 0x");
      if (address < 16) Serial.print("0");
      Serial.println(address, HEX);
    }
  }

  if (nDevices == 0) {
    Serial.println("No I2C devices found");
  } else {
    Serial.print("Found ");
    Serial.print(nDevices);
    Serial.println(" device(s)");
  }
  Serial.println();
}

// 测试特定地址是否有设备响应
bool testAddress(uint8_t addr) {
  Wire.beginTransmission(addr);
  return (Wire.endTransmission() == 0);
}

// 检查SC7I22连接
bool checkSC7I22() {
  uint8_t who_am_i = 0;

  Serial.print("Testing I2C communication with address 0x");
  Serial.println(SC7I22_I2C_ADDR, HEX);

  // 先测试基本I2C通信
  Wire.beginTransmission(SC7I22_I2C_ADDR);
  uint8_t error = Wire.endTransmission();
  if (error != 0) {
    Serial.print("I2C communication failed, error code: ");
    Serial.println(error);
    return false;
  }

  // 切换到bank 0
  Serial.println("Switching to bank 0...");
  if (!writeRegister(SC7I22_BANK_SEL, 0x00)) {
    Serial.println("Failed to write BANK_SEL register");
    return false;
  }
  delay(10);

  // 读取WHO_AM_I寄存器
  Serial.println("Reading WHO_AM_I register...");
  if (readRegister(SC7I22_WHO_AM_I, &who_am_i, 1)) {
    Serial.print("WHO_AM_I: 0x");
    Serial.println(who_am_i, HEX);
    if (who_am_i == 0x6A) {
      return true;
    } else {
      Serial.println("WHO_AM_I value incorrect, expected 0x6A");
      return false;
    }
  } else {
    Serial.println("Failed to read WHO_AM_I register");
    return false;
  }
}

// 配置SC7I22
bool configureSC7I22() {
  Serial.println("Starting SC7I22 configuration...");

  // 根据原始驱动，先进行软复位和bank切换
  Serial.println("Performing soft reset...");
  if (!writeRegister(SC7I22_PWR_CONF, 0x0E)) {
    Serial.println("Failed to write PWR_CONF");
    return false;
  }
  delay(10);

  // 确保在正确的bank
  Serial.println("Switching to bank 0...");
  if (!writeRegister(SC7I22_BANK_SEL, 0x00)) {
    Serial.println("Failed to switch to bank 0");
    return false;
  }
  delay(10);

  // 配置加速度计：100Hz, ±2G
  Serial.println("Configuring accelerometer...");
  if (!writeRegister(SC7I22_ACC_CONF, 0x88)) {
    Serial.println("Failed to configure ACC_CONF");
    return false;
  }
  delay(5);

  if (!writeRegister(SC7I22_ACC_RANGE, 0x01)) {
    Serial.println("Failed to configure ACC_RANGE");
    return false;
  }
  delay(5);

  // 配置陀螺仪：100Hz, ±2000dps
  Serial.println("Configuring gyroscope...");
  if (!writeRegister(SC7I22_GYR_CONF, 0xC8)) {
    Serial.println("Failed to configure GYR_CONF");
    return false;
  }
  delay(5);

  if (!writeRegister(SC7I22_GYR_RANGE, 0x00)) {
    Serial.println("Failed to configure GYR_RANGE");
    return false;
  }
  delay(5);

  // 配置I2C接口
  Serial.println("Configuring I2C interface...");
  if (!writeRegister(SC7I22_I2C_CFG, 0x50)) {
    Serial.println("Failed to configure I2C_CFG");
    return false;
  }

  delay(100);

  // 等待数据就绪
  Serial.println("Waiting for data ready...");
  uint8_t status = 0;
  uint16_t timeout = 0;
  while ((status & 0x03) != 0x03 && timeout < 10000) {
    if (!readRegister(SC7I22_STATUS, &status, 1)) {
      Serial.println("Failed to read status register");
      return false;
    }
    timeout++;
    if (timeout % 1000 == 0) {
      Serial.print("Waiting for data ready, status: 0x");
      Serial.print(status, HEX);
      Serial.print(", timeout: ");
      Serial.println(timeout);
    }
    delay(1);
  }

  if (timeout >= 10000) {
    Serial.println("Data ready timeout");
    return false;
  }

  Serial.println("Configuration completed successfully!");
  return true;
}

// 读取原始数据
bool readSC7I22RawData(IMU_Data* data) {
  uint8_t raw_data[12];
  uint8_t status = 0;
  uint16_t timeout = 0;
  
  // 等待数据就绪
  while ((status & 0x03) != 0x03 && timeout < 1000) {
    if (!readRegister(SC7I22_STATUS, &status, 1)) {
      return false;
    }
    timeout++;
    if (timeout >= 1000) {
      Serial.println("Data ready timeout");
      return false;
    }
    delayMicroseconds(100);
  }
  
  // 读取12字节原始数据（加速度计和陀螺仪各6字节）
  if (!readRegister(SC7I22_ACC_X_LSB, raw_data, 12)) {
    return false;
  }
  
  // 解析数据（高字节在前，低字节在后）
  data->acc_x = (int16_t)((raw_data[1] << 8) | raw_data[0]);
  data->acc_y = (int16_t)((raw_data[3] << 8) | raw_data[2]);
  data->acc_z = (int16_t)((raw_data[5] << 8) | raw_data[4]);
  data->gyr_x = (int16_t)((raw_data[7] << 8) | raw_data[6]);
  data->gyr_y = (int16_t)((raw_data[9] << 8) | raw_data[8]);
  data->gyr_z = (int16_t)((raw_data[11] << 8) | raw_data[10]);
  
  return true;
}

// 将原始数据转换为物理单位的辅助函数
float convertAcceleration(int16_t raw_value) {
  // ±2G范围，16位分辨率
  return (float)raw_value * 2.0 / 32768.0;
}

float convertGyroscope(int16_t raw_value) {
  // ±2000dps范围，16位分辨率
  return (float)raw_value * 2000.0 / 32768.0;
}

// 打印物理单位数据的函数（可选使用）
void printPhysicalData(IMU_Data* data) {
  Serial.println("Physical Units:");
  Serial.print("Acc(g): ");
  Serial.print(convertAcceleration(data->acc_x), 3);
  Serial.print(", ");
  Serial.print(convertAcceleration(data->acc_y), 3);
  Serial.print(", ");
  Serial.println(convertAcceleration(data->acc_z), 3);
  
  Serial.print("Gyr(dps): ");
  Serial.print(convertGyroscope(data->gyr_x), 3);
  Serial.print(", ");
  Serial.print(convertGyroscope(data->gyr_y), 3);
  Serial.print(", ");
  Serial.println(convertGyroscope(data->gyr_z), 3);
  Serial.println();
}

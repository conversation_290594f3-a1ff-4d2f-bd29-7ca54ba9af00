
#include "SL_SC7I22_Driver.h"
#include "I2C.h"
#include "usart.h"


#if SL_Sensor_Algo_Release_Enable==0x00
#include "usart.h"
#endif


//I2C SPIѡ��
#define SL_SC7I22_SPI_EN_I2C_DISABLE  0x00 //SL_SPI_IIC_INTERFACE ;
#define SL_SPI_IIC_INTERFACE           0x01 //SL_SC7I22_SPI_EN_I2C_DISABLE ;
//
#define SL_SC7I22_RAWDATA_HPF_ENABLE  0x00
//
#define SL_SC7I22_INT_DEFAULT_LEVEL   0x01
//SDO 
#define SL_SC7I22_SDO_PullUP_ENABLE   0x01

//FIFOʹ��
#define SL_SC7I22_FIFO_ENABLE   0x00

//FIFO_STREAMʹ�� 
#define SL_SC7I22_FIFO_STREAM   0x00

//FIFO_WTMʹ��
#define SL_SC7I22_FIFO_WTM   0x00
int sensor_status=0;
static void sl_delay(unsigned char sl_i)
{
    unsigned int sl_j = 10;

	sl_j = sl_j*1000*sl_i;
    while(sl_j--);
}


unsigned char SL_SC7I22_Check(void)
{
	unsigned char reg_value=0;
	
	SL_SC7I22_I2c_Spi_Write(SL_SPI_IIC_INTERFACE, 0x7F, 0x00);//goto 0x00
	SL_SC7I22_I2c_Spi_Read(SL_SPI_IIC_INTERFACE, SC7I22_WHO_AM_I, 1, &reg_value);
#if SL_Sensor_Algo_Release_Enable==0x00
	USART_printf( USART1, "0x%x=0x%x\r\n",SC7I22_WHO_AM_I,reg_value);
#endif
	if(reg_value==0x6A)
		return 0x01;//SC7I22
	else
		return 0x00;//IIC??
}


unsigned char SL_SC7I22_Config(void)
{
	unsigned char Check_Flag=0;
	unsigned char reg_value=0;
	unsigned short drdy_cnt=0;
	Check_Flag=SL_SC7I22_Check();
//	Check_Flag=1;//?????

	if(Check_Flag==1)
	{
		SL_SC7I22_I2c_Spi_Write(SL_SPI_IIC_INTERFACE, 0x7D, 0x0E);//goto 0x00
		sl_delay(10);	
		SL_SC7I22_I2c_Spi_Write(SL_SPI_IIC_INTERFACE, 0x40, 0x88);//ACC_CONF 100Hz
		SL_SC7I22_I2c_Spi_Write(SL_SPI_IIC_INTERFACE, 0x41, 0x01);//ACC_RANGE  ?G
		SL_SC7I22_I2c_Spi_Write(SL_SPI_IIC_INTERFACE, 0x42, 0xC8);//GYR_CONF 100Hz
		SL_SC7I22_I2c_Spi_Write(SL_SPI_IIC_INTERFACE, 0x43, 0x00);//GYR_RANGE 2000dps
		SL_SC7I22_I2c_Spi_Write(SL_SPI_IIC_INTERFACE, 0x05, 0x50);//I2C_CFG
#if SL_SC7I22_FIFO_ENABLE==0x01
		SL_SC7I22_I2c_Spi_Write(SL_SPI_IIC_INTERFACE, 0x05, 0x40);//I2C_CFG
		SL_SC7I22_I2c_Spi_Write(SL_SPI_IIC_INTERFACE, 0x06, 0x09);//FIFO_WTM:INT1
		SL_SC7I22_I2c_Spi_Write(SL_SPI_IIC_INTERFACE, 0x1C, 0x00);
		SL_SC7I22_I2c_Spi_Write(SL_SPI_IIC_INTERFACE, 0x1C, 0x06);//FIFO_EN
		SL_SC7I22_I2c_Spi_Write(SL_SPI_IIC_INTERFACE, 0x1D, 0x00);
		Check_Flag=0;
		while((Check_Flag&0x03)!=0x03)
		{
			i2cRead(0x19,0x0B,1,&Check_Flag);
			drdy_cnt++;
			if(drdy_cnt>10000) break;
			sl_delay(1);
		}	
	#if SL_SC7I22_FIFO_STREAM==0x01
	
	SL_SC7I22_I2c_Spi_Write(SL_SPI_IIC_INTERFACE, 0x1D, 0x20);
	SL_SC7I22_I2c_Spi_Write(SL_SPI_IIC_INTERFACE, 0x1E, 0xFF);
	sensor_status=0;
	#endif	
	#if SL_SC7I22_FIFO_WTM==0x01
	SL_SC7I22_I2c_Spi_Write(SL_SPI_IIC_INTERFACE, 0x1C, 0x06);//FIFO_EN
	SL_SC7I22_I2c_Spi_Write(SL_SPI_IIC_INTERFACE, 0x1D, 0x10);
	SL_SC7I22_I2c_Spi_Write(SL_SPI_IIC_INTERFACE, 0x1E, 0x2F);//FIFO-NUM	
	#endif			
#endif
		return 1;
	}
	else
		return 0;
}



unsigned int SL_SC7I22_TimeStamp_Read(void)
{
	unsigned char  time_data[3];
	unsigned int time_stamp;
	
	SL_SC7I22_I2c_Spi_Read(SL_SPI_IIC_INTERFACE, 0x18, 1, &time_data[0]);
	SL_SC7I22_I2c_Spi_Read(SL_SPI_IIC_INTERFACE, 0x19, 1, &time_data[1]);
	SL_SC7I22_I2c_Spi_Read(SL_SPI_IIC_INTERFACE, 0x20, 1, &time_data[2]);	

	time_stamp=(unsigned int)(time_data[0]<<16|time_data[1]<<8|time_data[2]);
	
	return time_stamp;
}

#if SL_SC7I22_FIFO_ENABLE ==0x00
void SL_SC7I22_RawData_Read(signed short * acc_data_buf,signed short * gyr_data_buf)
{
	unsigned char  raw_data[12];
	unsigned char  drdy_satus;
	unsigned short drdy_cnt=0;
	
	while((drdy_satus&0x03)!=0x03)
	{
		drdy_satus=0x00;
		SL_SC7I22_I2c_Spi_Read(SL_SPI_IIC_INTERFACE, 0x0B, 1, &drdy_satus);
		drdy_cnt++;
		if(drdy_cnt>10000) break;
		sl_delay(1);
	}

	SL_SC7I22_I2c_Spi_Read(SL_SPI_IIC_INTERFACE, 0x0C, 12, &raw_data[0]);	

	acc_data_buf[0] =(signed short)((((unsigned char)raw_data[0])* 256)  + ((unsigned char)raw_data[1]));//ACCX-16?
	acc_data_buf[1] =(signed short)((((unsigned char)raw_data[2])* 256)  + ((unsigned char)raw_data[3]));//ACCY-16?
	acc_data_buf[2] =(signed short)((((unsigned char)raw_data[4])* 256)  + ((unsigned char)raw_data[5]));//ACCZ-16?
	gyr_data_buf[0] =(signed short)((((unsigned char)raw_data[6])* 256)  + ((unsigned char)raw_data[7]));//GYRX-16?
	gyr_data_buf[1] =(signed short)((((unsigned char)raw_data[8])* 256)  + ((unsigned char)raw_data[9]));//GYRY-16?
	gyr_data_buf[2] =(signed short)((((unsigned char)raw_data[10])* 256) + ((unsigned char)raw_data[11]));//GYRZ-16?

}
#else

#if SL_Sensor_Algo_Release_Enable==0x00
#define SL_SC7I22_WAIT_FIFO_LEN_ENABLE 0x01
#else
#define SL_SC7I22_WAIT_FIFO_LEN_ENABLE 0x00
#endif
unsigned char  SL_SC7I22_FIFO_DATA[2048];

unsigned short SC7I22_FIFO_Read(signed short *accx_buf,signed short *accy_buf,signed short *accz_buf,signed short *gyrx_buf,signed short *gyry_buf,signed short *gyrz_buf)
{
	unsigned char  fifo_num1=0;
	unsigned char  fifo_num2=0;
	unsigned short fifo_num=0;
	signed short AX, AY ,AZ,GX,GY,GZ;
	unsigned short fifo_len=0;
	unsigned short j;
	fifo_num1=0;
	fifo_num2=0;
	SL_SC7I22_I2c_Spi_Read(SL_SPI_IIC_INTERFACE, 0x1F,1,&fifo_num1);
	SL_SC7I22_I2c_Spi_Read(SL_SPI_IIC_INTERFACE, 0x20,1,&fifo_num2);
	fifo_num=(fifo_num1&0x0F)*256+fifo_num2;
	
	SL_SC7I22_I2c_Spi_Read(SL_SPI_IIC_INTERFACE, 0x21, fifo_num*2, SL_SC7I22_FIFO_DATA);
#if SL_Sensor_Algo_Release_Enable==0
	USART_printf(USART1,"0x1F:0x%x 0x20:0x%x\n",fifo_num1,fifo_num2);
	USART_printf(USART1,"SC7I22_FIFO_NUM1:%d\n",fifo_num);
	USART_printf(USART1,"SC7I22_FIFO_NUM2:%d\n",(fifo_num-2)/3);//I2C=4 SPI=2
#endif
	for(j=0;j<(fifo_num-2)/6;j++)
	{
		//0-3=sensor timestamp
		gyrx_buf[j] =(signed short)(((unsigned char)SL_SC7I22_FIFO_DATA[j*12+4] * 256 ) + (unsigned char)SL_SC7I22_FIFO_DATA[j*12+5]);
		gyry_buf[j] =(signed short)(((unsigned char)SL_SC7I22_FIFO_DATA[j*12+6] * 256 ) + (unsigned char)SL_SC7I22_FIFO_DATA[j*12+7]);
		gyrz_buf[j] =(signed short)(((unsigned char)SL_SC7I22_FIFO_DATA[j*12+8] * 256 ) + (unsigned char)SL_SC7I22_FIFO_DATA[j*12+9]);
		accx_buf[j] =(signed short)(((unsigned char)SL_SC7I22_FIFO_DATA[j*12+10] * 256 ) + (unsigned char)SL_SC7I22_FIFO_DATA[j*12+11]);
		accy_buf[j] =(signed short)(((unsigned char)SL_SC7I22_FIFO_DATA[j*12+12] * 256 ) + (unsigned char)SL_SC7I22_FIFO_DATA[j*12+13]);
		accz_buf[j] =(signed short)(((unsigned char)SL_SC7I22_FIFO_DATA[j*12+14] * 256 ) + (unsigned char)SL_SC7I22_FIFO_DATA[j*12+15]);
	}
	
	fifo_num=(fifo_num-2)/6;
	
	#if SL_SC7I22_FIFO_STREAM==0x01
	SL_SC7I22_I2c_Spi_Write(SL_SPI_IIC_INTERFACE, 0x1D, 0x00);
	SL_SC7I22_I2c_Spi_Write(SL_SPI_IIC_INTERFACE, 0x1D, 0x20);
	#endif	
	
	#if SL_SC7I22_FIFO_WTM==0x01
	SL_SC7I22_I2c_Spi_Write(SL_SPI_IIC_INTERFACE, 0x1D, 0x00);
	SL_SC7I22_I2c_Spi_Write(SL_SPI_IIC_INTERFACE, 0x1D, 0x10);	
	#endif	

	return fifo_num;
}
#endif


unsigned char SL_SC7I22_POWER_DOWN(void)
{
	unsigned char SL_Read_Reg  = 0xff;
	
	SL_SC7I22_I2c_Spi_Write(SL_SPI_IIC_INTERFACE, 0x7D, 0x00);//POWER DOWN
	SL_SC7I22_I2c_Spi_Write(SL_SPI_IIC_INTERFACE, 0x7D, SL_Read_Reg);
	if(SL_Read_Reg==0x00)   return  1;
	else                    return  0;
}

